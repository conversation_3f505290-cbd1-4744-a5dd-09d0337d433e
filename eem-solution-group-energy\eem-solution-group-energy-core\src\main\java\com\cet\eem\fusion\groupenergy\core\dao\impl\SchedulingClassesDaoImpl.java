package com.cet.eem.fusion.groupenergy.core.dao.impl;

import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.piem.dao.classes.SchedulingClassesDao;
import com.cet.piem.entity.classes.SchedulingClasses;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 排班班次dao
 *
 * <AUTHOR>
 */
@Component
public class SchedulingClassesDaoImpl extends ModelDaoImpl<SchedulingClasses> implements SchedulingClassesDao {


    /**
     * 根据班次id查询排班班次
     *
     * @param classesConfigIds 班次id
     * @return 排班班次
     */
    @Override
    public List<SchedulingClasses> queryByClassesConfig(List<Long> classesConfigIds) {
        if (CollectionUtils.isEmpty(classesConfigIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SchedulingClasses> wrapper = LambdaQueryWrapper.of(SchedulingClasses.class)
                .in(SchedulingClasses::getClassConfigId, classesConfigIds);

        return selectList(wrapper);
    }


    /**
     * 根据班组查询排班班次
     *
     * @param teamGroupIds 班次id
     * @return 排班班次
     */
    @Override
    public List<SchedulingClasses> queryByTeamGroup(List<Long> teamGroupIds) {
        if (CollectionUtils.isEmpty(teamGroupIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SchedulingClasses> wrapper = LambdaQueryWrapper.of(SchedulingClasses.class)
                .in(SchedulingClasses::getTeamGroupId, teamGroupIds);
        return selectList(wrapper);
    }

    /**
     * 根据时间范围查询排班班次
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 排版班次
     */
    @Override
    public List<SchedulingClasses> queryByTimeRange(Long startTime, Long endTime,Long schedulingSchemeId) {

        if (Objects.isNull(startTime) || Objects.isNull(endTime)|| Objects.isNull(schedulingSchemeId)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SchedulingClasses> wrapper = LambdaQueryWrapper.of(SchedulingClasses.class)
                .ge(SchedulingClasses::getLogTime, startTime)
                .lt(SchedulingClasses::getLogTime, endTime)
                .eq(SchedulingClasses::getSchedulingSchemeId, schedulingSchemeId);
        return selectList(wrapper);
    }

    /**
     * 根据时间范围查询排班班次
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 排版班次
     */
    @Override
    public List<SchedulingClasses> queryByTimeRange(Long startTime, Long endTime) {

        if (Objects.isNull(startTime) || Objects.isNull(endTime)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<SchedulingClasses> wrapper = LambdaQueryWrapper.of(SchedulingClasses.class)
                .ge(SchedulingClasses::getLogTime, startTime)
                .lt(SchedulingClasses::getLogTime, endTime);
        return selectList(wrapper);
    }
}
