package com.cet.eem.fusion.groupenergy.core.dao.impl;

import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.piem.dao.classes.SchedulingSchemeToNodeDao;
import com.cet.piem.entity.classes.SchedulingSchemeToNode;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 方案关联节点dao层
 *
 * <AUTHOR>
 */
@Component
public class SchedulingSchemeToNodeDaoImpl extends ModelDaoImpl<SchedulingSchemeToNode> implements SchedulingSchemeToNodeDao {

    /**
     * 查询排班方案关联节点
     *
     * @param schedulingSchemeId 排班方案id
     * @return 关联节点
     */
    @Override
    public List<SchedulingSchemeToNode> queryBySchedulingSchemeId(Long schedulingSchemeId) {
        LambdaQueryWrapper<SchedulingSchemeToNode> wrapper = LambdaQueryWrapper.of(SchedulingSchemeToNode.class)
                .eq(SchedulingSchemeToNode::getSchedulingSchemeId, schedulingSchemeId);

        return selectList(wrapper);
    }
}
