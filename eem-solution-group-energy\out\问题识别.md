# 代码扫描问题报告

总问题数: 259

## ClassesConfig

### 问题 1
error_code: "import_issues"
missing_class: "ModelLabel"
calling_class: "ClassesConfig"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelLabel' 使用: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
line: ["[3, 19]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseEntity"
calling_class: "ClassesConfig"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseEntity' 使用: import com.cet.electric.baseconfig.common.base.BaseEntity;"
line: ["[4, 20]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "ClassesConfig"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5]"]

### 问题 4
error_type: "类问题"
error_code: "BaseEntity_issues"
calling_class: "ClassesConfig"
usage_pattern: "extends BaseEntity"
suggest: "请使用新的基础实体类替代BaseEntity"
line: ["20"]

## ClassesConfigDao

### 问题 1
error_code: "import_issues"
missing_class: "BaseModelDao"
calling_class: "ClassesConfigDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseModelDao' 使用: import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
line: ["[3, 11]"]

### 问题 2
error_code: "import_issues"
missing_class: "ClassesConfig"
calling_class: "ClassesConfigDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig;"
line: ["[4, 11]"]

## ClassesConfigDaoImpl

### 问题 1
error_code: "import_issues"
missing_class: "ModelDaoImpl"
calling_class: "ClassesConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelDaoImpl' 使用: import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
line: ["[3, 14]"]

### 问题 2
error_code: "import_issues"
missing_class: "ClassesConfigDao"
calling_class: "ClassesConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesConfigDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.ClassesConfigDao;"
line: ["[4, 14]"]

### 问题 3
error_code: "import_issues"
missing_class: "ClassesConfig"
calling_class: "ClassesConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig;"
line: ["[5, 14]"]

## ClassesConfigVO

### 问题 1
error_code: "import_issues"
missing_class: "ClassesConfig"
calling_class: "ClassesConfigVO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.vo"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesConfig;"
line: ["[3, 36]"]

## ClassesScheme

### 问题 1
error_code: "import_issues"
missing_class: "ModelLabel"
calling_class: "ClassesScheme"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelLabel' 使用: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
line: ["[3, 18]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseEntity"
calling_class: "ClassesScheme"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseEntity' 使用: import com.cet.electric.baseconfig.common.base.BaseEntity;"
line: ["[4, 19]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "ClassesScheme"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5]"]

### 问题 4
error_type: "类问题"
error_code: "BaseEntity_issues"
calling_class: "ClassesScheme"
usage_pattern: "extends BaseEntity"
suggest: "请使用新的基础实体类替代BaseEntity"
line: ["19"]

## ClassesSchemeDao

### 问题 1
error_code: "import_issues"
missing_class: "BaseModelDao"
calling_class: "ClassesSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseModelDao' 使用: import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
line: ["[3, 11]"]

### 问题 2
error_code: "import_issues"
missing_class: "ClassesScheme"
calling_class: "ClassesSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme;"
line: ["[4, 11, 18]"]

## ClassesSchemeDaoImpl

### 问题 1
error_code: "import_issues"
missing_class: "ModelDaoImpl"
calling_class: "ClassesSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelDaoImpl' 使用: import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
line: ["[3, 21]"]

### 问题 2
error_code: "import_issues"
missing_class: "ParentQueryConditionBuilder"
calling_class: "ClassesSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ParentQueryConditionBuilder' 使用: import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;"
line: ["[4, 35]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableColumnNameDef"
calling_class: "ClassesSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TableColumnNameDef' 使用: import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;"
line: ["[5]"]

### 问题 4
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "ClassesSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[6]"]

### 问题 5
error_code: "import_issues"
missing_class: "ClassesSchemeDao"
calling_class: "ClassesSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesSchemeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.ClassesSchemeDao;"
line: ["[7, 21]"]

### 问题 6
error_code: "import_issues"
missing_class: "ClassesScheme"
calling_class: "ClassesSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme;"
line: ["[8, 21, 30, 39, 39]"]

## ClassesSchemeVO

### 问题 1
error_code: "import_issues"
missing_class: "ClassesScheme"
calling_class: "ClassesSchemeVO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.vo"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.ClassesScheme;"
line: ["[3, 34]"]

## HolidayConfig

### 问题 1
error_code: "import_issues"
missing_class: "ModelLabel"
calling_class: "HolidayConfig"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelLabel' 使用: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
line: ["[3, 19]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseEntity"
calling_class: "HolidayConfig"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseEntity' 使用: import com.cet.electric.baseconfig.common.base.BaseEntity;"
line: ["[4, 20]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "HolidayConfig"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5]"]

### 问题 4
error_type: "类问题"
error_code: "BaseEntity_issues"
calling_class: "HolidayConfig"
usage_pattern: "extends BaseEntity"
suggest: "请使用新的基础实体类替代BaseEntity"
line: ["20"]

## HolidayConfigDao

### 问题 1
error_code: "import_issues"
missing_class: "BaseModelDao"
calling_class: "HolidayConfigDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseModelDao' 使用: import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
line: ["[3, 13]"]

### 问题 2
error_code: "import_issues"
missing_class: "HolidayConfig"
calling_class: "HolidayConfigDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'HolidayConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.HolidayConfig;"
line: ["[4, 21, 13]"]

## HolidayConfigDaoImpl

### 问题 1
error_code: "import_issues"
missing_class: "LambdaQueryWrapper"
calling_class: "HolidayConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'LambdaQueryWrapper' 使用以下路径之一: com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper, com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper"
line: ["[3, 34]"]

### 问题 2
error_code: "import_issues"
missing_class: "ModelDaoImpl"
calling_class: "HolidayConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelDaoImpl' 使用: import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
line: ["[4, 19]"]

### 问题 3
error_code: "import_issues"
missing_class: "HolidayConfigDao"
calling_class: "HolidayConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'HolidayConfigDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.HolidayConfigDao;"
line: ["[5, 19]"]

### 问题 4
error_code: "import_issues"
missing_class: "HolidayConfig"
calling_class: "HolidayConfigDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'HolidayConfig' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.HolidayConfig;"
line: ["[6, 19, 28, 34, 34]"]

## SchedulingClasses

### 问题 1
error_code: "import_issues"
missing_class: "ModelLabel"
calling_class: "SchedulingClasses"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelLabel' 使用: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
line: ["[3, 18]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseEntity"
calling_class: "SchedulingClasses"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseEntity' 使用: import com.cet.electric.baseconfig.common.base.BaseEntity;"
line: ["[4, 19]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "SchedulingClasses"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5]"]

### 问题 4
error_type: "类问题"
error_code: "BaseEntity_issues"
calling_class: "SchedulingClasses"
usage_pattern: "extends BaseEntity"
suggest: "请使用新的基础实体类替代BaseEntity"
line: ["19"]

## SchedulingClassesDao

### 问题 1
error_code: "import_issues"
missing_class: "BaseModelDao"
calling_class: "SchedulingClassesDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseModelDao' 使用: import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
line: ["[3, 13]"]

### 问题 2
error_code: "import_issues"
missing_class: "SchedulingClasses"
calling_class: "SchedulingClassesDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingClasses' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingClasses;"
line: ["[4, 30, 21, 48, 13, 39]"]

## SchedulingClassesDaoImpl

### 问题 1
error_code: "import_issues"
missing_class: "LambdaQueryWrapper"
calling_class: "SchedulingClassesDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'LambdaQueryWrapper' 使用以下路径之一: com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper, com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper"
line: ["[3, 90, 70, 52, 34]"]

### 问题 2
error_code: "import_issues"
missing_class: "ModelDaoImpl"
calling_class: "SchedulingClassesDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelDaoImpl' 使用: import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
line: ["[4, 20]"]

### 问题 3
error_code: "import_issues"
missing_class: "SchedulingClassesDao"
calling_class: "SchedulingClassesDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingClassesDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingClassesDao;"
line: ["[5, 20]"]

### 问题 4
error_code: "import_issues"
missing_class: "SchedulingClasses"
calling_class: "SchedulingClassesDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingClasses' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingClasses;"
line: ["[6, 85, 90, 90, 20, 65, 70, 70, 48, 52, 52, 30, 34, 34]"]

## SchedulingScheme

### 问题 1
error_code: "import_issues"
missing_class: "ModelLabel"
calling_class: "SchedulingScheme"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelLabel' 使用: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
line: ["[3, 22]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseEntity"
calling_class: "SchedulingScheme"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseEntity' 使用: import com.cet.electric.baseconfig.common.base.BaseEntity;"
line: ["[4, 23]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "SchedulingScheme"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5]"]

### 问题 4
error_code: "import_issues"
missing_class: "SchedulingSchemeAddUpdateDTO"
calling_class: "SchedulingScheme"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeAddUpdateDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeAddUpdateDTO;"
line: ["[6, 45]"]

### 问题 5
error_type: "类问题"
error_code: "BaseEntity_issues"
calling_class: "SchedulingScheme"
usage_pattern: "extends BaseEntity"
suggest: "请使用新的基础实体类替代BaseEntity"
line: ["23"]

## SchedulingSchemeDao

### 问题 1
error_code: "import_issues"
missing_class: "ResultWithTotal"
calling_class: "SchedulingSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[3, 24]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseModelDao"
calling_class: "SchedulingSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseModelDao' 使用: import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
line: ["[4, 15]"]

### 问题 3
error_code: "import_issues"
missing_class: "SchedulingScheme"
calling_class: "SchedulingSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;"
line: ["[5, 39, 46, 24, 31, 15]"]

### 问题 4
error_code: "import_issues"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "SchedulingSchemeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO;"
line: ["[6, 24]"]

### 问题 5
error_type: "类问题"
error_code: "method_return_type"
calling_class: "SchedulingSchemeDao"
calling_method: "pageQuery"
return_type: "ResultWithTotal<List<SchedulingScheme>>"
line: ["24"]

## SchedulingSchemeDaoImpl

### 问题 1
error_code: "import_issues"
missing_class: "ResultWithTotal"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[3, 45]"]

### 问题 2
error_code: "import_issues"
missing_class: "PageUtils"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'PageUtils' 使用: import com.cet.eem.fusion.common.utils.page.PageUtils;"
line: ["[4]"]

### 问题 3
error_code: "import_issues"
missing_class: "ModelDaoImpl"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelDaoImpl' 使用: import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
line: ["[5, 26]"]

### 问题 4
error_code: "import_issues"
missing_class: "ParentQueryConditionBuilder"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ParentQueryConditionBuilder' 使用: import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;"
line: ["[6, 99, 81, 46, 68]"]

### 问题 5
error_code: "import_issues"
missing_class: "TableColumnNameDef"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TableColumnNameDef' 使用: import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;"
line: ["[7]"]

### 问题 6
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[8]"]

### 问题 7
error_code: "import_issues"
missing_class: "SchedulingSchemeDao"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao;"
line: ["[9, 26]"]

### 问题 8
error_code: "import_issues"
missing_class: "SchedulingScheme"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;"
line: ["[10, 26, 98, 102, 80, 85, 85, 45, 55, 55, 57, 67, 70]"]

### 问题 9
error_code: "import_issues"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "SchedulingSchemeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.SchedulingSchemeQueryDTO;"
line: ["[11, 45]"]

### 问题 10
error_type: "类问题"
error_code: "method_return_type"
calling_class: "SchedulingSchemeDaoImpl"
calling_method: "pageQuery"
return_type: "ResultWithTotal<List<SchedulingScheme>>"
line: ["45"]

## SchedulingSchemeRelatedNodeDTO

### 问题 1
error_code: "import_issues"
missing_class: "BaseVo"
calling_class: "SchedulingSchemeRelatedNodeDTO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.dto"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseVo' 使用: import com.cet.eem.fusion.common.model.BaseVo;"
line: ["[3]"]

## SchedulingSchemeToNode

### 问题 1
error_code: "import_issues"
missing_class: "ModelLabel"
calling_class: "SchedulingSchemeToNode"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelLabel' 使用: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
line: ["[3, 18]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseEntity"
calling_class: "SchedulingSchemeToNode"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseEntity' 使用: import com.cet.electric.baseconfig.common.base.BaseEntity;"
line: ["[4, 19]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "SchedulingSchemeToNode"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5]"]

### 问题 4
error_type: "类问题"
error_code: "BaseEntity_issues"
calling_class: "SchedulingSchemeToNode"
usage_pattern: "extends BaseEntity"
suggest: "请使用新的基础实体类替代BaseEntity"
line: ["19"]

## SchedulingSchemeToNodeDao

### 问题 1
error_code: "import_issues"
missing_class: "BaseModelDao"
calling_class: "SchedulingSchemeToNodeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseModelDao' 使用: import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
line: ["[3, 13]"]

### 问题 2
error_code: "import_issues"
missing_class: "SchedulingSchemeToNode"
calling_class: "SchedulingSchemeToNodeDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeToNode' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode;"
line: ["[4, 21, 13]"]

## SchedulingSchemeToNodeDaoImpl

### 问题 1
error_code: "import_issues"
missing_class: "LambdaQueryWrapper"
calling_class: "SchedulingSchemeToNodeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'LambdaQueryWrapper' 使用以下路径之一: com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper, com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper"
line: ["[3, 27]"]

### 问题 2
error_code: "import_issues"
missing_class: "ModelDaoImpl"
calling_class: "SchedulingSchemeToNodeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelDaoImpl' 使用: import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
line: ["[4, 17]"]

### 问题 3
error_code: "import_issues"
missing_class: "SchedulingSchemeToNodeDao"
calling_class: "SchedulingSchemeToNodeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeToNodeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao;"
line: ["[5, 17]"]

### 问题 4
error_code: "import_issues"
missing_class: "SchedulingSchemeToNode"
calling_class: "SchedulingSchemeToNodeDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeToNode' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode;"
line: ["[6, 17, 26, 27, 27]"]

## SchedulingSchemeVO

### 问题 1
error_code: "import_issues"
missing_class: "SchedulingScheme"
calling_class: "SchedulingSchemeVO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.vo"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingScheme' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme;"
line: ["[3, 32]"]

## TeamConfigController

### 问题 1
error_code: "import_issues"
missing_class: "EnumAndOr"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'EnumAndOr' 使用: import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;"
line: ["[3]"]

### 问题 2
error_code: "import_issues"
missing_class: "OperationPermission"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'OperationPermission' 使用: import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;"
line: ["[4, 92, 61, 130, 77, 41, 138, 107, 122, 69, 153]"]

### 问题 3
error_code: "import_issues"
missing_class: "OperationLog"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'OperationLog' 使用以下路径之一: com.cet.eem.fusion.config.sdk.service.log.OperationLog, com.cet.electric.baseconfig.common.entity.OperationLog, com.cet.electric.model.definition.OperationLog"
line: ["[5, 91, 129, 76, 40, 137, 106, 121, 68, 152]"]

### 问题 4
error_code: "import_issues"
missing_class: "EnumOperationSubType"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'EnumOperationSubType' 使用: import com.cet.eem.fusion.common.utils.EnumOperationSubType;"
line: ["[6]"]

### 问题 5
error_code: "import_issues"
missing_class: "BaseVo"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseVo' 使用: import com.cet.eem.fusion.common.model.BaseVo;"
line: ["[7, 99]"]

### 问题 6
error_code: "import_issues"
missing_class: "Result"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'Result' 使用以下路径之一: com.cet.eem.fusion.common.entity.Result, com.cet.electric.matterhorn.cloud.authservice.common.entity.Result"
line: ["[8, 54, 93, 145, 114, 62, 171, 131, 78, 42, 99, 84, 108, 160, 154]"]

### 问题 7
error_code: "import_issues"
missing_class: "ResultWithTotal"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[9, 48]"]

### 问题 8
error_code: "import_issues"
missing_class: "SchedulingSchemeDetailVO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[54, 48, 62]"]

### 问题 9
error_code: "import_issues"
missing_class: "SchedulingSchemeRelatedNodeDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[93]"]

### 问题 10
error_code: "import_issues"
missing_class: "TeamGroupInfoVO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[145]"]

### 问题 11
error_code: "import_issues"
missing_class: "ClassesSchemeVO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[114]"]

### 问题 12
error_code: "import_issues"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[48]"]

### 问题 13
error_code: "import_issues"
missing_class: "SchedulingClassesVO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[171, 160]"]

### 问题 14
error_code: "import_issues"
missing_class: "TeamGroupInfoAddUpdateDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[131]"]

### 问题 15
error_code: "import_issues"
missing_class: "SchedulingSchemeRelatedHolidayDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[78]"]

### 问题 16
error_code: "import_issues"
missing_class: "SchedulingSchemeAddUpdateDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[42]"]

### 问题 17
error_code: "import_issues"
missing_class: "ClassesSchemeAddUpdateDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[108]"]

### 问题 18
error_code: "import_issues"
missing_class: "SchedulingClassesSaveDTO"
calling_class: "TeamConfigController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[154]"]

### 问题 19
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["35"]

### 问题 20
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateSchedulingScheme"
return_type: "Result<Boolean>"
line: ["42"]

### 问题 21
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateSchedulingScheme"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["40"]

### 问题 22
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "querySchedulingScheme"
return_type: "ResultWithTotal<List<SchedulingSchemeDetailVO>>"
line: ["48"]

### 问题 23
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "querySchedulingSchemeByType"
return_type: "Result<List<SchedulingSchemeDetailVO>>"
line: ["54"]

### 问题 24
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "allSchedulingScheme"
return_type: "Result<List<SchedulingSchemeDetailVO>>"
line: ["62"]

### 问题 25
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "deleteSchedulingScheme"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["68"]

### 问题 26
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingSchemeRelatedHoliday"
return_type: "Result<Boolean>"
line: ["78"]

### 问题 27
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingSchemeRelatedHoliday"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["76"]

### 问题 28
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "querySchedulingSchemeRelatedHoliday"
return_type: "Result<List<Long>>"
line: ["84"]

### 问题 29
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingSchemeRelatedNode"
return_type: "Result<Boolean>"
line: ["93"]

### 问题 30
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingSchemeRelatedNode"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["91"]

### 问题 31
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "querySchedulingSchemeRelatedNode"
return_type: "Result<List<BaseVo>>"
line: ["99"]

### 问题 32
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateClassesScheme"
return_type: "Result<Boolean>"
line: ["108"]

### 问题 33
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateClassesScheme"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["106"]

### 问题 34
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "queryClassesScheme"
return_type: "Result<List<ClassesSchemeVO>>"
line: ["114"]

### 问题 35
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "deleteClassesScheme"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["121"]

### 问题 36
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateTeamGroupInfo"
return_type: "Result<Boolean>"
line: ["131"]

### 问题 37
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "addOrUpdateTeamGroupInfo"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["129"]

### 问题 38
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "deleteTeamGroupInfo"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["137"]

### 问题 39
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "queryTeamGroupInfo"
return_type: "Result<List<TeamGroupInfoVO>>"
line: ["145"]

### 问题 40
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingClasses"
return_type: "Result<Boolean>"
line: ["154"]

### 问题 41
error_type: "权限 ID 调整详细方案"
error_code: "annotation_content"
calling_class: "TeamConfigController"
calling_method: "saveSchedulingClasses"
suggest: "@OperationLog中值异常，融合后的日志编码operationType值使用范围在[10000, 20000]以内"
line: ["152"]

### 问题 42
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "querySchedulingClasses"
return_type: "Result<List<SchedulingClassesVO>>"
line: ["160"]

### 问题 43
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigController"
calling_method: "querySchedulingClassesTeamGroupInfo"
return_type: "Result<List<SchedulingClassesVO>>"
line: ["171"]

## TeamConfigService

### 问题 1
error_code: "import_issues"
missing_class: "BaseVo"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseVo' 使用: import com.cet.eem.fusion.common.model.BaseVo;"
line: ["[4, 79]"]

### 问题 2
error_code: "import_issues"
missing_class: "ResultWithTotal"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5, 31]"]

### 问题 3
error_code: "import_issues"
missing_class: "ClassesSchemeVO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[95]"]

### 问题 4
error_code: "import_issues"
missing_class: "SchedulingClassesSaveDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[135]"]

### 问题 5
error_code: "import_issues"
missing_class: "SchedulingSchemeDetailVO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[161, 31, 38]"]

### 问题 6
error_code: "import_issues"
missing_class: "TeamGroupInfoVO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[127]"]

### 问题 7
error_code: "import_issues"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[31]"]

### 问题 8
error_code: "import_issues"
missing_class: "SchedulingSchemeAddUpdateDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[23]"]

### 问题 9
error_code: "import_issues"
missing_class: "SchedulingSchemeRelatedHolidayDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[55]"]

### 问题 10
error_code: "import_issues"
missing_class: "SchedulingClassesVO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[144, 154]"]

### 问题 11
error_code: "import_issues"
missing_class: "SchedulingSchemeRelatedNodeDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[71]"]

### 问题 12
error_code: "import_issues"
missing_class: "ClassesSchemeAddUpdateDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[87]"]

### 问题 13
error_code: "import_issues"
missing_class: "TeamGroupInfoAddUpdateDTO"
calling_class: "TeamConfigService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[111]"]

### 问题 14
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigService"
calling_method: "querySchedulingScheme"
return_type: "ResultWithTotal<List<SchedulingSchemeDetailVO>>"
line: ["31"]

## TeamConfigServiceImpl

### 问题 1
error_code: "import_issues"
missing_class: "ParamUtils"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ParamUtils' 使用: import com.cet.eem.fusion.common.utils.ParamUtils;"
line: ["[3]"]

### 问题 2
error_code: "import_issues"
missing_class: "BusinessBaseException"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BusinessBaseException' 使用: import com.cet.eem.fusion.common.exception.BusinessBaseException;"
line: ["[4, 140, 158, 429, 502, 297, 302, 315, 344, 360, 76, 450, 465, 475]"]

### 问题 3
error_code: "import_issues"
missing_class: "BaseVo"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseVo' 使用: import com.cet.eem.fusion.common.model.BaseVo;"
line: ["[5, 272, 277, 249, 251]"]

### 问题 4
error_code: "import_issues"
missing_class: "Result"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'Result' 使用以下路径之一: com.cet.eem.fusion.common.entity.Result, com.cet.electric.matterhorn.cloud.authservice.common.entity.Result"
line: ["[6, 531]"]

### 问题 5
error_code: "import_issues"
missing_class: "ResultWithTotal"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ResultWithTotal' 已废弃，请寻找替代方案或移除相关代码"
line: ["[7, 91, 93, 95, 95]"]

### 问题 6
error_code: "import_issues"
missing_class: "UserVo"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'UserVo' 使用: import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;"
line: ["[8, 531, 534, 538]"]

### 问题 7
error_code: "import_issues"
missing_class: "JsonTransferUtils"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'JsonTransferUtils' 使用以下路径之一: com.cet.eem.fusion.common.utils.JsonTransferUtils, com.cet.electric.baseconfig.sdk.common.utils.JsonTransferUtils, com.cet.electric.modelservice.sdk.toolkit.JsonTransferUtils"
line: ["[9]"]

### 问题 8
error_code: "import_issues"
missing_class: "TimeUtil"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TimeUtil' 使用以下路径之一: com.cet.eem.fusion.common.utils.time.TimeUtil, com.cet.electric.baseconfig.sdk.common.utils.TimeUtil, com.cet.electric.fusion.matrix.v2.utils.TimeUtil"
line: ["[10]"]

### 问题 9
error_code: "import_issues"
missing_class: "EemCloudAuthService"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'EemCloudAuthService' 已废弃，请寻找替代方案或移除相关代码"
line: ["[11]"]

### 问题 10
error_code: "import_issues"
missing_class: "TeamConfigService"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamConfigService' 使用: import com.cet.eem.fusion.groupenergy.core.service.TeamConfigService;"
line: ["[12, 30]"]

### 问题 11
error_code: "import_issues"
missing_class: "SchedulingSchemeDetailVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[109, 91, 94, 95, 761, 762, 764, 764, 745]"]

### 问题 12
error_code: "import_issues"
missing_class: "SchedulingScheme"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[110, 93, 392, 126, 761, 763, 516, 305, 66, 79, 79, 442, 746]"]

### 问题 13
error_code: "import_issues"
missing_class: "SchedulingClassesVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[598, 621, 623, 623, 668, 696, 698, 698]"]

### 问题 14
error_code: "import_issues"
missing_class: "SchedulingClasses"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[604, 615, 622, 627, 569, 575, 577, 577, 675, 690, 697, 702, 138, 156, 427, 500, 358, 463]"]

### 问题 15
error_code: "import_issues"
missing_class: "TeamGroupInfo"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[618, 630, 693, 705, 782, 521, 447, 454, 454, 471, 479, 479]"]

### 问题 16
error_code: "import_issues"
missing_class: "ClassesConfig"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[619, 640, 694, 715, 319, 321, 321, 354, 366, 368, 368]"]

### 问题 17
error_code: "import_issues"
missing_class: "SchedulingClassesConfigVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[626, 628, 628, 701, 703, 703]"]

### 问题 18
error_code: "import_issues"
missing_class: "SchedulingSchemeQueryDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[91]"]

### 问题 19
error_code: "import_issues"
missing_class: "HolidayConfig"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[222, 194, 203, 205, 205]"]

### 问题 20
error_code: "import_issues"
missing_class: "SchedulingClassesSaveDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[567]"]

### 问题 21
error_code: "import_issues"
missing_class: "SchedulingClassesConfigDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[576]"]

### 问题 22
error_code: "import_issues"
missing_class: "SchedulingSchemeToNode"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[273, 164, 239, 250, 252, 252]"]

### 问题 23
error_code: "import_issues"
missing_class: "ClassesSchemeVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[390, 400, 402, 402, 772, 774, 774]"]

### 问题 24
error_code: "import_issues"
missing_class: "ClassesScheme"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[401, 147, 416, 773, 311, 330, 330, 340, 348]"]

### 问题 25
error_code: "import_issues"
missing_class: "SchedulingSchemeRelatedHolidayDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[191]"]

### 问题 26
error_code: "import_issues"
missing_class: "TeamGroupInfoVO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[781, 783, 783, 514, 515, 522, 522]"]

### 问题 27
error_code: "import_issues"
missing_class: "SchedulingSchemeRelatedNodeDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[237]"]

### 问题 28
error_code: "import_issues"
missing_class: "ClassesSchemeAddUpdateDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[289]"]

### 问题 29
error_code: "import_issues"
missing_class: "ClassesConfigDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[290, 292, 293, 320, 367]"]

### 问题 30
error_code: "import_issues"
missing_class: "SchedulingSchemeAddUpdateDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[64]"]

### 问题 31
error_code: "import_issues"
missing_class: "TeamGroupInfoAddUpdateDTO"
calling_class: "TeamConfigServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[438]"]

### 问题 32
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["32"]

### 问题 33
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["35"]

### 问题 34
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["38"]

### 问题 35
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["41"]

### 问题 36
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["44"]

### 问题 37
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["47"]

### 问题 38
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["50"]

### 问题 39
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamConfigServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["53"]

### 问题 40
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamConfigServiceImpl"
calling_method: "querySchedulingScheme"
return_type: "ResultWithTotal<List<SchedulingSchemeDetailVO>>"
line: ["91"]

### 问题 41
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamConfigServiceImpl"
calling_method: "TeamConfigServiceImpl -> EemCloudAuthService"
suggest: "EemCloudAuthService废弃"
line: ["{'声明': 54, '使用': [531]}"]

### 问题 42
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamConfigServiceImpl"
calling_method: "TeamConfigServiceImpl -> NodeDao"
suggest: "NodeDao已经废弃，考虑通过EemNodeService重构"
line: ["{'声明': 39, '使用': [164, 167, 239, 245, 260, 273]}"]

## TeamEnergyController

### 问题 1
error_code: "import_issues"
missing_class: "Result"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'Result' 使用以下路径之一: com.cet.eem.fusion.common.entity.Result, com.cet.electric.matterhorn.cloud.authservice.common.entity.Result"
line: ["[3, 37, 43, 55, 61, 49]"]

### 问题 2
error_code: "import_issues"
missing_class: "ClassesEnergyInfoQueryDTO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO;"
line: ["[5, 55]"]

### 问题 3
error_code: "import_issues"
missing_class: "TeamGroupEnergyInfoQueryDTO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO;"
line: ["[6, 37, 43, 49]"]

### 问题 4
error_code: "import_issues"
missing_class: "ClassesEnergyInfoVO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO;"
line: ["[7, 55, 49]"]

### 问题 5
error_code: "import_issues"
missing_class: "TeamGroupEnergyHistogramVO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyHistogramVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO;"
line: ["[8, 43]"]

### 问题 6
error_code: "import_issues"
missing_class: "TeamGroupEnergyInfoVO"
calling_class: "TeamEnergyController"
old_dependency: "com.cet.eem.fusion.groupenergy.core.controller"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO;"
line: ["[9, 37]"]

### 问题 7
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["32"]

### 问题 8
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamEnergyController"
calling_method: "queryTeamGroupEnergyInfo"
return_type: "Result<TeamGroupEnergyInfoVO>"
line: ["37"]

### 问题 9
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamEnergyController"
calling_method: "queryTeamGroupEnergyHistogram"
return_type: "Result<List<TeamGroupEnergyHistogramVO>>"
line: ["43"]

### 问题 10
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamEnergyController"
calling_method: "queryClassesEnergyCompare"
return_type: "Result<List<ClassesEnergyInfoVO>>"
line: ["49"]

### 问题 11
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamEnergyController"
calling_method: "queryClassesEnergy"
return_type: "Result<ClassesEnergyInfoVO>"
line: ["55"]

### 问题 12
error_type: "类问题"
error_code: "method_return_type"
calling_class: "TeamEnergyController"
calling_method: "classesEnergyProjectTree"
return_type: "Result<List<Map<String, Object>>>"
line: ["61"]

## TeamEnergyService

### 问题 1
error_code: "import_issues"
missing_class: "ClassesEnergyInfoQueryDTO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO;"
line: ["[3, 49]"]

### 问题 2
error_code: "import_issues"
missing_class: "TeamGroupEnergyInfoQueryDTO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO;"
line: ["[4, 33, 25, 41]"]

### 问题 3
error_code: "import_issues"
missing_class: "ClassesEnergyInfoVO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO;"
line: ["[5, 49, 41]"]

### 问题 4
error_code: "import_issues"
missing_class: "TeamGroupEnergyHistogramVO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyHistogramVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO;"
line: ["[6, 33]"]

### 问题 5
error_code: "import_issues"
missing_class: "TeamGroupEnergyInfoVO"
calling_class: "TeamEnergyService"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO;"
line: ["[7, 25]"]

## TeamEnergyServiceImpl

### 问题 1
error_code: "import_issues"
missing_class: "ProjectUnitClassify"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ProjectUnitClassify' 使用: import com.cet.eem.fusion.common.def.base.ProjectUnitClassify;"
line: ["[3]"]

### 问题 2
error_code: "import_issues"
missing_class: "UserDefineUnit"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'UserDefineUnit' 使用: import com.cet.electric.baseconfig.common.entity.UserDefineUnit;"
line: ["[4, 257, 404, 302, 88, 186]"]

### 问题 3
error_code: "import_issues"
missing_class: "CommonUtils"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'CommonUtils' 使用以下路径之一: com.cet.eem.fusion.common.utils.CommonUtils, com.cet.electric.matterhorn.devicedataservice.common.utils.CommonUtils"
line: ["[5]"]

### 问题 4
error_code: "import_issues"
missing_class: "EnumOperationType"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'EnumOperationType' 使用: import com.cet.eem.fusion.common.def.common.EnumOperationType;"
line: ["[6]"]

### 问题 5
error_code: "import_issues"
missing_class: "ColumnDef"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ColumnDef' 使用: import com.cet.eem.fusion.common.def.common.ColumnDef;"
line: ["[7]"]

### 问题 6
error_code: "import_issues"
missing_class: "BaseVo"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseVo' 使用: import com.cet.eem.fusion.common.model.BaseVo;"
line: ["[8, 481, 497, 497, 469, 469]"]

### 问题 7
error_code: "import_issues"
missing_class: "TimeUtil"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TimeUtil' 使用以下路径之一: com.cet.eem.fusion.common.utils.time.TimeUtil, com.cet.electric.baseconfig.sdk.common.utils.TimeUtil, com.cet.electric.fusion.matrix.v2.utils.TimeUtil"
line: ["[9]"]

### 问题 8
error_code: "import_issues"
missing_class: "TableColumnNameDef"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TableColumnNameDef' 使用: import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;"
line: ["[10]"]

### 问题 9
error_code: "import_issues"
missing_class: "AggregationCycle"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'AggregationCycle' 使用以下路径之一: com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle, com.cet.electric.baseconfig.sdk.common.def.AggregationCycle, com.cet.electric.fusion.matrix.v2.utils.AggregationCycle"
line: ["[11]"]

### 问题 10
error_code: "import_issues"
missing_class: "DoubleUtils"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'DoubleUtils' 使用: import com.cet.eem.solution.common.utils.DoubleUtils;"
line: ["[12]"]

### 问题 11
error_code: "import_issues"
missing_class: "SchedulingSchemeDao"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao;"
line: ["[13]"]

### 问题 12
error_code: "import_issues"
missing_class: "SchedulingSchemeToNodeDao"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'SchedulingSchemeToNodeDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao;"
line: ["[14]"]

### 问题 13
error_code: "import_issues"
missing_class: "TeamGroupEnergyDao"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao;"
line: ["[15]"]

### 问题 14
error_code: "import_issues"
missing_class: "ClassesEnergyInfoQueryDTO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.ClassesEnergyInfoQueryDTO;"
line: ["[17, 372]"]

### 问题 15
error_code: "import_issues"
missing_class: "TeamGroupEnergyInfoQueryDTO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoQueryDTO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.dto.TeamGroupEnergyInfoQueryDTO;"
line: ["[18, 271, 65, 153]"]

### 问题 16
error_code: "import_issues"
missing_class: "ClassesEnergyInfoVO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ClassesEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO;"
line: ["[19, 372, 373, 373, 436, 446, 446, 271, 272, 310, 310, 319, 319]"]

### 问题 17
error_code: "import_issues"
missing_class: "TeamGroupEnergyCard"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyCard' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyCard;"
line: ["[20, 101, 103, 103]"]

### 问题 18
error_code: "import_issues"
missing_class: "TeamGroupEnergyHistogramVO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyHistogramVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO;"
line: ["[21, 153, 190, 193, 193, 204, 217, 217, 232, 232]"]

### 问题 19
error_code: "import_issues"
missing_class: "TeamGroupEnergyInfoVO"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyInfoVO' 使用: import com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO;"
line: ["[22, 65, 67, 67]"]

### 问题 20
error_code: "import_issues"
missing_class: "UnitService"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'UnitService' 已废弃，请寻找替代方案或移除相关代码"
line: ["[23]"]

### 问题 21
error_code: "import_issues"
missing_class: "NodeServiceImpl"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'NodeServiceImpl' 使用: import com.cet.electric.baseconfig.sdk.service.impl.NodeServiceImpl;"
line: ["[24]"]

### 问题 22
error_code: "import_issues"
missing_class: "TeamEnergyService"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamEnergyService' 使用: import com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService;"
line: ["[25, 41]"]

### 问题 23
error_code: "import_issues"
missing_class: "SchedulingScheme"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[376, 275, 70, 161]"]

### 问题 24
error_code: "import_issues"
missing_class: "ClassesConfig"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[382, 441, 281, 320, 170, 219]"]

### 问题 25
error_code: "import_issues"
missing_class: "ClassesScheme"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[383, 437, 282]"]

### 问题 26
error_code: "import_issues"
missing_class: "TeamGroupEnergy"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[396, 407, 294, 305, 316, 80, 98, 102, 106, 178, 199, 204, 205, 208, 216, 217, 217, 232, 232]"]

### 问题 27
error_code: "import_issues"
missing_class: "TeamGroupInfo"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[427, 76, 108, 167, 211]"]

### 问题 28
error_code: "import_issues"
missing_class: "ClassesName"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'ClassesName' 已废弃，请寻找替代方案或移除相关代码"
line: ["[436, 446, 446, 319, 319]"]

### 问题 29
error_code: "import_issues"
missing_class: "SchedulingSchemeToNode"
calling_class: "TeamEnergyServiceImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.service.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "请添加新的import类导入"
line: ["[468]"]

### 问题 30
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["43"]

### 问题 31
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["46"]

### 问题 32
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["49"]

### 问题 33
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["52"]

### 问题 34
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["55"]

### 问题 35
error_type: "类问题"
error_code: "CommonUtilsCalcDouble_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "CommonUtils.calcDouble"
suggest: "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换"
line: ["94"]

### 问题 36
error_type: "类问题"
error_code: "CommonUtilsCalcDouble_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "CommonUtils.calcDouble"
suggest: "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换"
line: ["116"]

### 问题 37
error_type: "类问题"
error_code: "CommonUtilsCalcDouble_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "CommonUtils.calcDouble"
suggest: "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换"
line: ["118"]

### 问题 38
error_type: "类问题"
error_code: "CommonUtilsCalcDouble_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "CommonUtils.calcDouble"
suggest: "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换"
line: ["261"]

### 问题 39
error_type: "类问题"
error_code: "CommonUtilsCalcDouble_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "CommonUtils.calcDouble"
suggest: "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换"
line: ["351"]

### 问题 40
error_type: "类问题"
error_code: "CommonUtilsCalcDouble_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "CommonUtils.calcDouble"
suggest: "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换"
line: ["354"]

### 问题 41
error_type: "类问题"
error_code: "CommonUtilsCalcDouble_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "CommonUtils.calcDouble"
suggest: "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换"
line: ["414"]

### 问题 42
error_type: "类问题"
error_code: "CommonUtilsCalcDouble_issues"
calling_class: "TeamEnergyServiceImpl"
usage_pattern: "CommonUtils.calcDouble"
suggest: "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换"
line: ["416"]

### 问题 43
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> NodeService"
suggest: "请使用EemNodeService替代NodeService"
line: ["{'声明': 53, '使用': [466]}"]

### 问题 44
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> NodeServiceImpl"
suggest: "请使用EemNodeService替代NodeServiceImpl"
line: ["{'声明': 53, '使用': [466]}"]

### 问题 45
error_type: "单位服务变更详细信息"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> UnitService"
suggest: "UnitService已经废弃，考虑通过EnergyUnitService重构"
line: ["{'声明': 50, '使用': [88, 186, 302, 404]}"]

### 问题 46
error_type: "单位服务变更详细信息"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> UnitService"
suggest: "UnitService已经废弃，考虑通过EnergyUnitService重构"
line: ["{'声明': 50, '使用': [88, 186, 302, 404]}"]

### 问题 47
error_type: "类问题"
error_code: "target_detection"
calling_class: "TeamEnergyServiceImpl"
calling_method: "TeamEnergyServiceImpl -> NodeDao"
suggest: "NodeDao已经废弃，考虑通过EemNodeService重构"
line: ["{'声明': 56, '使用': [468]}"]

## TeamGroupEnergy

### 问题 1
error_code: "import_issues"
missing_class: "ModelLabel"
calling_class: "TeamGroupEnergy"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelLabel' 使用: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
line: ["[3, 18]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseEntity"
calling_class: "TeamGroupEnergy"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseEntity' 使用: import com.cet.electric.baseconfig.common.base.BaseEntity;"
line: ["[4, 19]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "TeamGroupEnergy"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5]"]

### 问题 4
error_type: "类问题"
error_code: "BaseEntity_issues"
calling_class: "TeamGroupEnergy"
usage_pattern: "extends BaseEntity"
suggest: "请使用新的基础实体类替代BaseEntity"
line: ["19"]

## TeamGroupEnergyDao

### 问题 1
error_code: "import_issues"
missing_class: "BaseModelDao"
calling_class: "TeamGroupEnergyDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseModelDao' 使用: import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
line: ["[3, 13]"]

### 问题 2
error_code: "import_issues"
missing_class: "TeamGroupEnergy"
calling_class: "TeamGroupEnergyDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergy' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy;"
line: ["[4, 54, 27, 40, 13]"]

## TeamGroupEnergyDaoImpl

### 问题 1
error_code: "import_issues"
missing_class: "LambdaQueryWrapper"
calling_class: "TeamGroupEnergyDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'LambdaQueryWrapper' 使用以下路径之一: com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper, com.cet.electric.modelservice.sdk.conditions.query.LambdaQueryWrapper"
line: ["[3, 39, 68, 99]"]

### 问题 2
error_code: "import_issues"
missing_class: "ModelDaoImpl"
calling_class: "TeamGroupEnergyDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelDaoImpl' 使用: import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
line: ["[4, 20]"]

### 问题 3
error_code: "import_issues"
missing_class: "AggregationCycle"
calling_class: "TeamGroupEnergyDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'AggregationCycle' 使用以下路径之一: com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle, com.cet.electric.baseconfig.sdk.common.def.AggregationCycle, com.cet.electric.fusion.matrix.v2.utils.AggregationCycle"
line: ["[5]"]

### 问题 4
error_code: "import_issues"
missing_class: "TeamGroupEnergyDao"
calling_class: "TeamGroupEnergyDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergyDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao;"
line: ["[6, 20]"]

### 问题 5
error_code: "import_issues"
missing_class: "TeamGroupEnergy"
calling_class: "TeamGroupEnergyDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupEnergy' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy;"
line: ["[7, 34, 39, 39, 64, 68, 68, 20, 94, 99, 99]"]

## TeamGroupInfo

### 问题 1
error_code: "import_issues"
missing_class: "ModelLabel"
calling_class: "TeamGroupInfo"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelLabel' 使用: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;"
line: ["[3, 19]"]

### 问题 2
error_code: "import_issues"
missing_class: "BaseEntity"
calling_class: "TeamGroupInfo"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseEntity' 使用: import com.cet.electric.baseconfig.common.base.BaseEntity;"
line: ["[4, 20]"]

### 问题 3
error_code: "import_issues"
missing_class: "TableNameDef"
calling_class: "TeamGroupInfo"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.po"
current_dependency: "eem-solution-group-energy-core"
suggest: "类 'TableNameDef' 已废弃，请寻找替代方案或移除相关代码"
line: ["[5]"]

### 问题 4
error_type: "类问题"
error_code: "BaseEntity_issues"
calling_class: "TeamGroupInfo"
usage_pattern: "extends BaseEntity"
suggest: "请使用新的基础实体类替代BaseEntity"
line: ["20"]

## TeamGroupInfoDao

### 问题 1
error_code: "import_issues"
missing_class: "BaseModelDao"
calling_class: "TeamGroupInfoDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'BaseModelDao' 使用: import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;"
line: ["[3, 11]"]

### 问题 2
error_code: "import_issues"
missing_class: "TeamGroupInfo"
calling_class: "TeamGroupInfoDao"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupInfo' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo;"
line: ["[4, 11]"]

## TeamGroupInfoDaoImpl

### 问题 1
error_code: "import_issues"
missing_class: "ModelDaoImpl"
calling_class: "TeamGroupInfoDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'ModelDaoImpl' 使用: import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;"
line: ["[3, 14]"]

### 问题 2
error_code: "import_issues"
missing_class: "TeamGroupInfoDao"
calling_class: "TeamGroupInfoDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupInfoDao' 使用: import com.cet.eem.fusion.groupenergy.core.dao.TeamGroupInfoDao;"
line: ["[4, 14]"]

### 问题 3
error_code: "import_issues"
missing_class: "TeamGroupInfo"
calling_class: "TeamGroupInfoDaoImpl"
old_dependency: "com.cet.eem.fusion.groupenergy.core.dao.impl"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupInfo' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo;"
line: ["[5, 14]"]

## TeamGroupInfoVO

### 问题 1
error_code: "import_issues"
missing_class: "TeamGroupInfo"
calling_class: "TeamGroupInfoVO"
old_dependency: "com.cet.eem.fusion.groupenergy.core.entity.vo"
current_dependency: "eem-solution-group-energy-core"
suggest: "建议为类 'TeamGroupInfo' 使用: import com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupInfo;"
line: ["[3, 3, 39, 39]"]
