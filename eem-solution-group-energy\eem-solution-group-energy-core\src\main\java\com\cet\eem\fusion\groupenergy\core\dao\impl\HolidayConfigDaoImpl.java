package com.cet.eem.fusion.groupenergy.core.dao.impl;

import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.piem.dao.classes.HolidayConfigDao;
import com.cet.piem.entity.classes.HolidayConfig;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 方案节假日信息dao层
 *
 * <AUTHOR>
 */
@Component
public class HolidayConfigDaoImpl extends ModelDaoImpl<HolidayConfig> implements HolidayConfigDao {

    /**
     * 查询方案节假日信息
     *
     * @param schedulingSchemeId 方案id
     * @return 节假日信息
     */
    @Override
    public List<HolidayConfig> queryBySchedulingScheme(Long schedulingSchemeId) {

        if (Objects.isNull(schedulingSchemeId)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<HolidayConfig> wrapper = LambdaQueryWrapper.of(HolidayConfig.class)
                .eq(HolidayConfig::getSchedulingSchemeId, schedulingSchemeId);
        return selectList(wrapper);
    }
}
