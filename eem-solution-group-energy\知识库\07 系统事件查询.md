# 07 系统事件查询

# SystemEventService 使用文档

## 概述

SystemEventService 是融合矩阵SDK中的系统事件服务接口，提供系统事件的查询功能。该服务主要用于查询和管理电力系统中的各种事件信息，支持按条件查询和批量查询。

## Maven依赖

在您的项目中添加以下依赖：

```plaintext
<dependency>
    <groupId>com.cet.electric</groupId>
    <artifactId>model-sdk-event</artifactId>
    <version>1.0.12.16</version>
</dependency>
```

## 接口方法详解

### 1. 按条件查询系统事件列表

```plaintext
ListWithTotal<SystemEvent> querySystemEventList(EventQueryDTO eventQueryDTO);
```

#### 方法说明

根据查询条件查询系统事件列表，支持分页和多种过滤条件。

#### 参数说明

**EventQueryDTO 查询参数对象：**

| **字段名** | **类型** | **必填** | **说明** |
| --- | --- | --- | --- |
| nodes | List<BaseEntity> | 是 | 查询的节点列表，包含modelLabel和id |
| starTime | Long | 是 | 起始时间（Unix时间戳，毫秒） |
| endTime | Long | 是 | 结束时间（Unix时间戳，毫秒） |
| energyType | Integer | 是 | 能源类型 |
| timeAsc | boolean | 否 | 是否按时间升序排列，默认降序 |
| status | Integer | 否 | 事件确认状态 |
| eventType | Integer | 否 | 事件类型 |
| cycle | Integer | 否 | 聚合周期 |
| level | Integer | 否 | 事件优先级等级 |
| index | Integer | 否 | 分页页码（从0开始） |
| limit | Integer | 否 | 每页数量 |

#### 返回值

`ListWithTotal<SystemEvent>` - 包含总数和数据列表的分页结果

#### 使用示例

```plaintext
@Service
public class YourBusinessService {
    
    @Autowired
    private SystemEventService systemEventService;
    
    public void queryEvents() {
        // 构建查询节点
        List<BaseEntity> nodes = Arrays.asList(
            BaseEntity.builder()
                .id(1001L)
                .modelLabel("transformer")
                .build(),
            BaseEntity.builder()
                .id(1002L)
                .modelLabel("generator")
                .build()
        );
        
        // 构建查询条件
        EventQueryDTO queryDTO = EventQueryDTO.builder()
            .nodes(nodes)
            .starTime(System.currentTimeMillis() - 24 * 60 * 60 * 1000L) // 24小时前
            .endTime(System.currentTimeMillis()) // 当前时间
            .energyType(1) // 电力类型
            .status(0) // 未确认状态
            .eventType(1) // 告警事件
            .level(2) // 中等优先级
            .index(0) // 第一页
            .limit(20) // 每页20条
            .build();
        
        // 执行查询
        ListWithTotal<SystemEvent> result = systemEventService.querySystemEventList(queryDTO);
        
        System.out.println("总数: " + result.getTotal());
        System.out.println("当前页数据: " + result.getList().size());
        
        // 处理查询结果
        for (SystemEvent event : result.getList()) {
            System.out.println("事件名称: " + event.getName());
            System.out.println("事件时间: " + new Date(event.getEventTime()));
            System.out.println("事件描述: " + event.getDescription());
        }
    }
}
```

### 2. 根据ID批量查询系统事件

```plaintext
List<SystemEvent> querySystemEventList(List<Long> ids);
```

#### 方法说明

根据事件ID列表批量查询系统事件信息。

#### 参数说明

*   `ids`: 事件ID列表，不能为空
    

#### 返回值

`List<SystemEvent>` - 系统事件列表

#### 使用示例

```plaintext
@Service
public class YourBusinessService {
    
    @Autowired
    private SystemEventService systemEventService;
    
    public void queryEventsByIds() {
        // 事件ID列表
        List<Long> eventIds = Arrays.asList(1001L, 1002L, 1003L);
        
        // 批量查询
        List<SystemEvent> events = systemEventService.querySystemEventList(eventIds);
        
        // 处理结果
        events.forEach(event -> {
            System.out.println("事件ID: " + event.getId());
            System.out.println("事件名称: " + event.getName());
            System.out.println("关联对象: " + event.getObjectLabel() + ":" + event.getObjectId());
        });
    }
}
```

## 数据模型说明

### SystemEvent 系统事件实体

SystemEvent 继承自BaseEntity，主要字段说明：

| **字段名** | **类型** | **说明** |
| --- | --- | --- |
| id | Long | 事件唯一标识（继承自BaseEntity） |
| name | String | 事件名称/标题 |
| description | String | 事件详细描述信息 |
| eventTime | Long | 事件发生时间（Unix时间戳） |
| eventType | Integer | 事件类型标识 |
| level | Integer | 事件优先级等级 |
| confirmEventStatus | Integer | 事件确认状态 |
| energyType | Integer | 能源类型 |
| objectId | Long | 关联设备/对象唯一标识 |
| objectLabel | String | 对象业务标签 |
| operator | String | 操作人员账号 |
| operatorId | Long | 操作人员系统ID |
| remark | String | 补充备注信息 |
| updateTime | Long | 最后更新时间 |
| calcBaseline | Double | 基准值计算结果 |
| aggregationCycle | Integer | 聚合周期 |
| endTime | Long | 结束时间 |
| convergentGroup | String | 收敛组 |
| convergentStatus | Boolean | 收敛状态 |
| extend | SystemEventExtendDTO | 事件扩展属性 |

### EventQueryDTO 查询参数对象

```plaintext
@Data
@NoArgsConstructor
@Builder
public class EventQueryDTO {
    @ApiModelProperty("节点")
    private List<BaseEntity> nodes;
    
    @ApiModelProperty("起始时间")
    private Long starTime;
    
    @ApiModelProperty("结束时间")
    private Long endTime;
    
    @ApiModelProperty("是否按照时间升序")
    private boolean timeAsc;
    
    @ApiModelProperty("能源类型")
    private Integer energyType;
    
    @ApiModelProperty("事件状态")
    private Integer status;
    
    @ApiModelProperty("事件类型")
    private Integer eventType;
    
    @ApiModelProperty("周期")
    private Integer cycle;
    
    @ApiModelProperty("事件等级")
    private Integer level;
    
    @ApiModelProperty("分页")
    private Integer index;
    
    @ApiModelProperty("分页")
    private Integer limit;
}
```

## 注意事项

1.  **节点参数必填**: 在使用 `querySystemEventList(EventQueryDTO)` 方法时，`nodes` 参数不能为空，否则返回空结果。
    
2.  **时间范围**: `starTime` 和 `endTime` 必须提供，且使用Unix时间戳（毫秒级）。
    
3.  **分页参数**: 如果需要分页，`index` 和 `limit` 必须同时提供，页码从0开始。
    
4.  **性能考虑**: 
    
    *   避免查询过大的时间范围
        
    *   合理设置分页大小
        
    *   尽量使用具体的过滤条件减少数据量
        
5.  **空值处理**: 服务会自动处理空参数，避免空指针异常。
    

## 错误处理

```plaintext
@Service
public class YourBusinessService {
    
    @Autowired
    private SystemEventService systemEventService;
    
    public void safeQueryEvents() {
        try {
            EventQueryDTO queryDTO = EventQueryDTO.builder()
                .nodes(Collections.emptyList()) // 空节点列表
                .starTime(System.currentTimeMillis() - 3600000L)
                .endTime(System.currentTimeMillis())
                .energyType(1)
                .build();
            
            ListWithTotal<SystemEvent> result = systemEventService.querySystemEventList(queryDTO);
            
            if (result.getTotal() == 0) {
                System.out.println("未查询到符合条件的事件");
            }
            
        } catch (Exception e) {
            System.err.println("查询系统事件失败: " + e.getMessage());
            // 记录日志或其他错误处理
        }
    }
}
```

## 完整使用示例

### Controller层示例

```plaintext
@RestController
@RequestMapping("/api/events")
public class SystemEventController {
    
    @Autowired
    private SystemEventService systemEventService;
    
    /**
     * 查询系统事件列表
     */
    @PostMapping("/query")
    public ApiResult<ListWithTotal<SystemEvent>> queryEvents(@RequestBody EventQueryDTO queryDTO) {
        try {
            // 参数校验
            if (CollectionUtils.isEmpty(queryDTO.getNodes())) {
                return ApiResult.error("查询节点不能为空");
            }
            
            if (queryDTO.getStarTime() == null || queryDTO.getEndTime() == null) {
                return ApiResult.error("时间范围不能为空");
            }
            
            // 执行查询
            ListWithTotal<SystemEvent> result = systemEventService.querySystemEventList(queryDTO);
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            return ApiResult.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID批量查询事件
     */
    @PostMapping("/batch")
    public ApiResult<List<SystemEvent>> batchQuery(@RequestBody List<Long> ids) {
        try {
            if (CollectionUtils.isEmpty(ids)) {
                return ApiResult.error("事件ID列表不能为空");
            }
            
            List<SystemEvent> events = systemEventService.querySystemEventList(ids);
            
            return ApiResult.success(events);
            
        } catch (Exception e) {
            return ApiResult.error("批量查询失败: " + e.getMessage());
        }
    }
}
```

### Service层示例

```plaintext
@Service
public class EventBusinessService {
    
    @Autowired
    private SystemEventService systemEventService;
    
    /**
     * 查询最近24小时的告警事件
     */
    public List<SystemEvent> getRecentAlarmEvents(List<BaseEntity> nodes, Integer energyType) {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 24 * 60 * 60 * 1000L; // 24小时前
        
        EventQueryDTO queryDTO = EventQueryDTO.builder()
            .nodes(nodes)
            .starTime(startTime)
            .endTime(endTime)
            .energyType(energyType)
            .eventType(1) // 告警类型
            .status(0) // 未确认
            .level(2) // 中等及以上级别
            .index(0)
            .limit(100)
            .build();
        
        ListWithTotal<SystemEvent> result = systemEventService.querySystemEventList(queryDTO);
        return result.getList();
    }
    
    /**
     * 统计事件数量
     */
    public Map<String, Long> getEventStatistics(List<BaseEntity> nodes, Integer energyType, 
                                               long startTime, long endTime) {
        EventQueryDTO queryDTO = EventQueryDTO.builder()
            .nodes(nodes)
            .starTime(startTime)
            .endTime(endTime)
            .energyType(energyType)
            .build();
        
        ListWithTotal<SystemEvent> result = systemEventService.querySystemEventList(queryDTO);
        
        Map<String, Long> statistics = new HashMap<>();
        statistics.put("total", result.getTotal());
        
        // 按事件类型统计
        Map<Integer, Long> typeCount = result.getList().stream()
            .collect(Collectors.groupingBy(SystemEvent::getEventType, Collectors.counting()));
        
        typeCount.forEach((type, count) -> 
            statistics.put("type_" + type, count));
        
        return statistics;
    }
}
```

## 配置说明

### Spring Boot配置

在 `application.yml` 中可以配置相关参数：

```plaintext
# 数据库配置
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.cet.electric.modelsdk.event.model

# 日志配置
logging:
  level:
    com.cet.electric.modelsdk.event: DEBUG
```

## 常见问题

### Q1: 查询返回空结果

**A**: 检查以下几点：

*   nodes参数是否为空
    
*   时间范围是否正确
    
*   energyType是否匹配
    
*   数据库中是否存在对应数据
    

### Q2: 分页不生效

**A**: 确保同时设置了index和limit参数，且index从0开始。

### Q3: 性能问题

**A**: 

*   缩小时间查询范围
    
*   减少nodes数量
    
*   添加更多过滤条件
    
*   合理设置分页大小
    

### Q4: 时间格式问题

**A**: 确保使用Unix时间戳（毫秒级），可以使用 `System.currentTimeMillis()` 获取当前时间戳。

**注意**: 本文档基于当前版本编写，如有更新请及时查看最新版本文档。