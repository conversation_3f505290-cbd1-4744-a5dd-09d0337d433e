# 03 节点操作

## 节点操作

### 节点相关操作（SDK）

1.  【Service类】节点相关操作：EemNodeService
    
2.  使用：
    

```java
    @Autowired
    EemNodeService eemNodeService;
```

3.  提供方法如下：
    

1.  查询带有名称的节点信息：List<BaseVo> queryNodeWithName(Collection<BaseVo> nodes)
    
2.  查询节点名称信息：List<EntityWithName> queryNodeName(Collection<? extends BaseEntity> nodes)
    
3.  查询节点名称映射关系：Map<EntityWithName, String> queryAbstractNodeNameMap(Collection<? extends BaseEntity> nodes)
    
4.  查询节点树结构：List<NodeTreeDTO> queryNodeTree(NodeTreeQueryDTO nodeTreeQueryDTO)
    
5.  查询节点树结构：List<NodeTreeDTO> queryNodeTree(NodeTreeAnalysisQueryDTO nodeTreeQueryDTO)
    
6.  查询节点树结构（不进行权限验证）：List<NodeTreeDTO> queryNodeTreeNoAuth(NodeTreeAnalysisQueryDTO nodeTreeQueryDTO)
    
7.  查询节点树结构（不进行权限验证）：List<NodeTreeDTO> queryNodeTreeNoAuth(NodeTreeQueryDTO nodeTreeQueryDTO)
    
8.  获取节点树结构（不进行权限验证）：List<NodeTreeDTO> queryNodeTreeNoAuth(Long tenantId, Integer nodeTreeGroupId, Integer energyType)
    
9.  批量查询节点详细信息：List<Map<String, Object>> queryNodeDetails(List<LabelAndId> labelAndIds)
    
10.  查询子节点标签（需权限验证）：List<NodeTreeDTO> queryChildLabelAuth(SubConditionQueryDTO childLabelQueryDTO)
    
11.  批量查询节点的父节点信息：List<NodeParents> queryParentNodeBatch(List<? extends BaseEntity> nodeList)
    
12.  批量查询节点的父节点路径：Map<EntityWithName, List<String>> queryParentNodePath(List<? extends BaseEntity> nodeList)
    
13.  查询子节点标签（无需权限验证）：List<NodeTreeDTO> queryChildLabelNoAuth(SubConditionQueryDTO queryDTO)
    
14.  查询根节点树结构（需权限验证）：List<NodeTreeDTO> queryRootNodeAuth(Long tenantId, Long userId)
    
15.  查询根节点树结构（需权限验证）,如无对应根节点全部权限，则不返回：List<NodeTreeDTO> queryRootNodeAuthFilter(Long tenantId, Long userId)
    
16.  查询所有根节点：List<BaseEntity> queryRootNodes()
    
17.  根据BaseVo集合查询指定类型的节点列表：<T> List<T> queryNodes(Collection<BaseVo> nodes, Class<T> clazz)
    
18.  查询有权限访问的节点集合：Set<EntityWithName> queryAuthNodes(NodeTreeQueryDTO queryDTO)
    
19.  根据节点查询父节点的信息：Map<LabelAndId, Tree> queryFatherNodeByNodeList(List<LabelAndId> nodeList)
    
20.  根据节点查询根节点的信息：Map<LabelAndId, Tree> queryRootNodeByNodeList(List<LabelAndId> nodeList)
    
21.  查询指定根节点的市政总管节点：Map<BaseEntity, List<EntityWithName>> queryCivicPipe(@NotEmpty Collection<BaseEntity> rootNodeList)
    
22.  查询与指定标签相关联的节点树结构：List<NodeTreeDTO> queryRelatedNode(List<BaseEntity> nodeList, String relatedLabel)
    

### 节点相关操作（接口）

1.  提供节点树接口的查询，通过继承的方法对外提供接口（EemBaseNodeManageController）
    
2.  示例如下：
    

```java
@Api(value = PluginInfoDef.BaseConfig.INTERFACE_PREFIX + "/v1/node", tags = "项目节点管理")
@RequestMapping(value = PluginInfoDef.BaseConfig.INTERFACE_PREFIX + "/v1/node")
@RestController
@Validated
public class EemNodeManageController extends EemBaseNodeManageController
```

3.  方法如下：
    

1.  查询指定租户或者节点下的节点树，在节点未指定的情况下查询租户的：public ApiResult<List<NodeTreeDTO>> queryNodeProperty(@RequestBody NodeTreeQueryDTO nodeTreeQueryDTO)
    
2.  根据指定的业务查询指定租户或者节点下的节点树，在节点未指定的情况下查询租户的：public ApiResult<List<NodeTreeDTO>> queryNodeTree(@RequestBody NodeTreeAnalysisQueryDTO nodeTreeQueryDTO)
    
3.  去除分摊之后的节点树：public ApiResult<List<NodeTreeDTO>> queryNodeTreeNoShare(@RequestBody NodeTreeQueryDTO nodeTreeQueryDTO)
    
4.  根据指定的业务查询去除分摊之后的节点树：public ApiResult<List<NodeTreeDTO>> queryNodeTreeNoShare(@RequestBody NodeTreeAnalysisQueryDTO nodeTreeQueryDTO)
    
5.  查询节点详情：public ApiResult<List<Map<String, Object>>> queryNodeProperty(@RequestBody List<LabelAndId> nodes)
    
6.  查询指定子层级信息：public ApiResult<List<NodeTreeDTO>> queryNodeProperty(@RequestBody SubConditionQueryDTO childLabelQueryDTO)
    
7.  查询设备分类数据：public ApiResult<List<Map<String, Object>>> queryNodeProperty(@RequestParam(required = false) String name)
    
8.  查询根节点：public ApiResult<List<NodeTreeDTO>> queryNodeProperty()
    

## 节点树结构

### 节点树结构（SDK）

1.  【Service类】节点相关操作：EemTreeConfigService
    
2.  使用：
    

```java
    @Autowired
    EemTreeConfigService eemTreeConfigService;
```

3.  提供方法如下：
    

1.  获取节点树分组集合：List<Integer> getRelationGroupList(Integer nodeTreeGroupId)
    
2.  根据节点树分组id查询节点关系树：List<NodeRelationTree> queryNodeRelationTree(Integer nodeTreeGroupId)
    
3.  查询子节点关系树，不包含当前层级：List<NodeRelationTree> queryChildNodeRelationTreeWithOutCurrent(NodeTreeConfigDTO nodeTreeConfigDTO)
    
4.  查询子节点关系树：List<NodeRelationTree> queryChildNodeRelationTree(NodeTreeConfigDTO nodeTreeConfigDTO)
    
5.  获取管理节点树：List<NodeRelationTree> getManageNodeTree()
    
6.  获取所有子管理级别节点：List<ManageNodeVo> getAllSonManageLevel(String parentLabel, Integer subType)
    
7.  获取所有子级别节点：List<ManageNodeVo> getAllSonLevel(String parentLabel, Integer subType, Integer nodeTreeGroupId)
    
8.  获取根节点标签：NodeRelationTree getRootLabel()
    
9.  获取节点父路径：Map<ManageNodeVo, List<ManageNodeVo>> getNodeParentPath(List<NodeRelationTree> nodeRelationTrees)
    
10.  获取父节点路径（包含当前节点）：Map<ManageNodeVo, List<ManageNodeVo>> getNodeParentPath(Integer nodeTreeGroupId, Set<ManageNodeVo> queryNodeInfoSet)
    

### 节点树结构（接口）

1.  提供节点树结构的查询，可以继承自这个类，获取树结构；
    
2.  使用方法：
    

```java
@Api(value = PluginInfoDef.BaseConfig.INTERFACE_PREFIX + "/v1/tree-config", tags = "项目结构管理")
@RequestMapping(value = PluginInfoDef.BaseConfig.INTERFACE_PREFIX + "/v1/tree-config")
@RestController
@Validated
public class EemTreeConfigController extends EemBaseTreeConfigController {

}
```

3.  方法如下：
    

1.  查询全量节点树结构：public ApiResult<List<NodeRelationTree>> queryNodeRelationTree(@RequestParam(required = false) @ApiParam(value = "节点树分组id，如果为空则返回全量节点树", name = "nodeTreeGroupId") Integer nodeTreeGroupId)
    
2.  查询指定节点类型以及子级：public ApiResult<List<NodeRelationTree>> addNodes(@RequestBody NodeTreeConfigDTO nodeTreeConfigDTO)
    
3.  查询根节点：public ApiResult<NodeRelationTree> getRootLabel()