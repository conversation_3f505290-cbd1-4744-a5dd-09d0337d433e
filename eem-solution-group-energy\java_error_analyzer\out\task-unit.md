# 单位服务变更问题分析和解决方案

## 任务执行概述

**任务**: 1.5 单位服务变更问题分析和解决方案确定
**数据来源**: out\问题识别.md
**问题范围**: 基于知识库第5类"单位服务变更"
**处理策略**: 分段读取，逐个验证，实时统计

## 问题识别和统计

### 搜索关键词
- UnitService
- UserDefineUnit  
- UserDefineUnitDTO
- getUnit
- queryUnitCoef
- EnergyUnitService
- ProjectUnitClassify

### 问题统计结果
- **总搜索行数**: 2334行
- **匹配问题数**: 5个（去重后）
- **涉及文件数**: 1个
- **主要问题类型**: 服务类变更、实体类变更、方法签名变更

## 按文件维度的问题分析和解决方案

## TeamEnergyServiceImpl.java

### 单位服务变更问题 1: ProjectUnitClassify 类导入 (🟢 绿色标记)

- **问题位置**: 行号 3
- **问题类型**: Import 问题（与单位服务相关）
- **缺失类**: ProjectUnitClassify
- **知识库解决方案**: 基于知识库第5类"单位服务变更"，ProjectUnitClassify是单位查询中的关键枚举类
- **解决方案**: import com.cet.eem.fusion.common.def.base.ProjectUnitClassify;
- **修复操作**: 在文件顶部添加导入语句
- **业务说明**: 用于区分能耗(ENERGY)和产量(PRODUCT)单位类型

### 单位服务变更问题 2: UserDefineUnit 实体类变更 (🟢 绿色标记)

- **问题位置**: 行号 4, 88, 186, 257, 302, 404
- **问题类型**: 实体类变更
- **旧实体**: UserDefineUnit
- **新实体**: UserDefineUnitDTO
- **知识库解决方案**: 基于知识库第5类"单位服务变更"中的实体变更指导
- **解决方案**: 
  ```java
  // 原代码
  import com.cet.electric.baseconfig.common.entity.UserDefineUnit;
  
  // 新代码
  import com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO;
  ```
- **修复操作**: 
  1. 更新import语句
  2. 将所有UserDefineUnit类型声明替换为UserDefineUnitDTO
  3. 验证方法调用兼容性
- **注意事项**: UserDefineUnitDTO是UserDefineUnit的父类，大多数情况下可以直接替换

### 单位服务变更问题 3: UnitService 类导入变更 (🟢 绿色标记)

- **问题位置**: 行号 23
- **问题类型**: Import 问题（服务类变更）
- **旧服务**: UnitService
- **新服务**: EnergyUnitService
- **知识库解决方案**: 基于知识库第5类"单位服务变更"中的包和类名变更
- **解决方案**:
  ```java
  // 原代码
  import com.cet.piem.service.UnitService;
  
  // 新代码
  import com.cet.eem.fusion.config.sdk.service.EnergyUnitService;
  ```
- **修复操作**: 更新import语句，将UnitService替换为EnergyUnitService

### 单位服务变更问题 4: UnitService 服务声明和使用变更 (🟢 绿色标记)

- **问题位置**: 声明行号 50，使用行号 88, 186, 302, 404
- **问题类型**: 服务类变更和方法签名变更
- **旧服务**: UnitService
- **新服务**: EnergyUnitService
- **方法变更**: getUnit() → queryUnitCoef()
- **知识库解决方案**: 基于知识库第5类"单位服务变更"中的方法签名变更指导
- **解决方案**:
  ```java
  // 原代码
  @Resource
  private UnitService unitService;
  
  List<Double> energyValueList = teamGroupEnergyList.stream()
      .map(TeamGroupEnergy::getValue).collect(Collectors.toList());
  UserDefineUnit unit = unitService.getUnit(energyValueList, 
      ProjectUnitClassify.ENERGY, dto.getEnergyType());
  
  // 新代码
  import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
  import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;
  
  @Resource
  private EnergyUnitService energyUnitService;
  
  Double maxValue = teamGroupEnergyList.stream()
      .map(TeamGroupEnergy::getValue).max(Double::compareTo).orElse(null);
  UserDefineUnitDTO userDefineUnitDTO = energyUnitService.queryUnitCoef(
      new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), 
          dto.getEnergyType(), ProjectUnitClassify.ENERGY, maxValue));
  ```
- **修复操作**:
  1. 更新服务声明：UnitService → EnergyUnitService
  2. 更新方法调用：getUnit() → queryUnitCoef()
  3. 更新参数结构：使用UserDefineUnitSearchDTO
  4. 更新返回类型：UserDefineUnit → UserDefineUnitDTO
  5. 添加必要的import语句
- **业务规则适配**:
  - 能耗查询：传值ProjectUnitClassify.ENERGY
  - 产量查询：传值ProjectUnitClassify.PRODUCT
  - 需要传入租户ID：GlobalInfoUtils.getTenantId()
  - 参数变更：从值列表改为最大值

## 处理完成统计

### 问题处理汇总
- **处理文件数**: 1个
- **单位服务变更问题总数**: 5个（去重后为4个独立问题）
- **解决方案分类**:
  - 🟢 **绿色标记（有明确知识库解决方案）**: 4个
  - 🔴 **红色标记（无法确定解决方案）**: 0个

### 详细问题映射
1. **ProjectUnitClassify导入** → 🟢 有明确import路径
2. **UserDefineUnit实体变更** → 🟢 有明确替换方案
3. **UnitService导入变更** → 🟢 有明确新服务类
4. **UnitService使用变更** → 🟢 有完整的方法迁移指导

### 验证结果
- ✅ **问题识别完整性**: 通过关键词搜索确认覆盖所有相关问题
- ✅ **解决方案完整性**: 每个问题都有基于知识库的明确解决方案
- ✅ **文件维度组织**: 按TeamEnergyServiceImpl.java文件组织
- ✅ **修复操作具体性**: 提供了详细的代码替换指导

### 知识库匹配验证
- **第5类单位服务变更**: 100%匹配，所有问题都在知识库覆盖范围内
- **包和类名变更**: ✅ UnitService → EnergyUnitService
- **实体变更**: ✅ UserDefineUnit → UserDefineUnitDTO  
- **方法签名变更**: ✅ getUnit() → queryUnitCoef()
- **业务规则适配**: ✅ 能耗/产量分开查询

## 执行建议

### 修复顺序建议
1. **第一步**: 更新所有import语句
2. **第二步**: 更新实体类型声明
3. **第三步**: 更新服务声明
4. **第四步**: 更新方法调用和参数结构
5. **第五步**: 验证编译和功能正确性

### 注意事项
- 所有变更都有明确的知识库支持，可以直接执行
- 需要注意业务逻辑的适配，特别是能耗和产量的区分
- 建议在修复后进行充分的功能测试
- 关注租户ID的正确传递

## 任务完成确认

✅ **任务1.5执行完成**
- 成功识别了所有单位服务变更相关问题
- 为每个问题提供了基于知识库的明确解决方案
- 按文件维度组织了输出结果
- 确保了问题处理的完整性和准确性
