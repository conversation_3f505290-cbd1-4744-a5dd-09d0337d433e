package com.cet.eem.fusion.groupenergy.core.def;

/**
 * 班组能耗插件常量定义
 *
 * <AUTHOR>
 */
public class GroupEnergyConstantDef {

    /**
     * 排班方案
     */
    public static final int SCHEDULING_SCHEME = 122;

    /**
     * 班次方案
     */
    public static final int CLASSES_SCHEME = 123;

    /**
     * 班组信息
     */
    public static final int TEAM_GROUP_INFO = 124;

    /**
     * 排班班次
     */
    public static final int SCHEDULING_CLASSES = 125;

    /**
     * 排班方案录入
     */
    public static final String SCHEDULINGSCHEME_INSPECT = "schedulingscheme_inspect";

    /**
     * 排班方案更新
     */
    public static final String SCHEDULINGSCHEME_UPDATE = "schedulingscheme_update";

    /**
     * 排班方案查询
     */
    public static final String SCHEDULINGSCHEME_BROWSER = "schedulingscheme_browser";

    /**
     * 排班方案删除
     */
    public static final String SCHEDULINGSCHEME_DELETE = "schedulingscheme_delete";
}
