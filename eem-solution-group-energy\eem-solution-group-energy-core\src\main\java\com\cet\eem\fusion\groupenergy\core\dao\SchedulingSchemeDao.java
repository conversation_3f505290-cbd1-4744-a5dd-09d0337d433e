package com.cet.eem.fusion.groupenergy.core.dao;

import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;
import com.cet.piem.entity.classes.SchedulingScheme;
import com.cet.piem.entity.dto.classes.SchedulingSchemeQueryDTO;

import java.util.List;

/**
 * 排班方案dao层
 *
 * <AUTHOR>
 */
public interface SchedulingSchemeDao extends BaseModelDao<SchedulingScheme> {


    /**
     * 查询排班方案
     *
     * @param dto 条件
     * @return 排班方案
     */
    ResultWithTotal<List<SchedulingScheme>> pageQuery(SchedulingSchemeQueryDTO dto);

    /**
     * 查询所有排班方案以及班次班组
     *
     * @return 排班方案
     */
    List<SchedulingScheme> queryAll();

    /**
     * 根据id查询关联节点
     *
     * @param id 排版方案id
     * @return 排版方案和班次班组
     */
    SchedulingScheme queryAssociationNodeById(Long id);

    /**
     * 查询生产类型排班方案
     *
     * @return 生产类型排班方案
     */
    List<SchedulingScheme> queryProduceSchedulingScheme(Integer classTeamType);

}
