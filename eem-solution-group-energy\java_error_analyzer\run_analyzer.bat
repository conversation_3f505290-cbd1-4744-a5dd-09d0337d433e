@echo off
REM Java Error Analyzer 运行脚本
REM 使用方法: run_analyzer.bat [XML文件路径] [项目路径]

echo ========================================
echo Java Error Analyzer 启动中...
echo ========================================

REM 设置默认参数
set XML_FILE=..\JavaAnnotator.xml
set PROJECT_PATH=..\
set OUTPUT_DIR=.\output

REM 如果提供了参数，使用用户指定的参数
if not "%1"=="" set XML_FILE=%1
if not "%2"=="" set PROJECT_PATH=%2

echo XML文件: %XML_FILE%
echo 项目路径: %PROJECT_PATH%
echo 输出目录: %OUTPUT_DIR%
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH环境变量
    pause
    exit /b 1
)

REM 检查XML文件是否存在
if not exist "%XML_FILE%" (
    echo 错误: XML文件不存在: %XML_FILE%
    echo 请确保JavaAnnotator.xml文件存在
    pause
    exit /b 1
)

REM 运行分析器
echo 开始分析...
python "%~dp0main.py" --xml-file "%XML_FILE%" --project-path "%PROJECT_PATH%" --output-dir "%OUTPUT_DIR%"

if errorlevel 1 (
    echo.
    echo 分析过程中出现错误，请查看日志文件
) else (
    echo.
    echo 分析完成！请查看输出目录中的结果文件
)

echo.
pause
