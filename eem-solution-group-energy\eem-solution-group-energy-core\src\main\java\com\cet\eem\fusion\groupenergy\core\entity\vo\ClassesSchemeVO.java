package com.cet.eem.fusion.groupenergy.core.entity.vo;

import com.cet.piem.entity.classes.ClassesScheme;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 班次方案vo
 *
 * <AUTHOR>
 */
@ApiModel(value = "ClassesSchemeVO", description = "班次方案vo")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ClassesSchemeVO {

    @ApiModelProperty(value = "班次方案id，更新传递")
    private Long id;

    @ApiModelProperty(value = "班次方案名称")
    private String name;

    @ApiModelProperty(value = "班次配置")
    private List<ClassesConfigVO> classesConfigList;

    public ClassesSchemeVO(ClassesScheme classesScheme) {
        this.id = classesScheme.getId();
        this.name = classesScheme.getName();
        if (CollectionUtils.isNotEmpty(classesScheme.getClassesconfig_model())) {
            this.classesConfigList = classesScheme.getClassesconfig_model().stream()
                    .map(ClassesConfigVO::new)
                    .collect(Collectors.toList());
        }

    }
}
