"""
类名查找器实现 - 集成FindNameFromJar2.py脚本
"""
import subprocess
import logging
import os
import re
from typing import List, Optional
try:
    from .interfaces import ClassNameFinderInterface
    from .models import ProcessingConfig
except ImportError:
    from interfaces import ClassNameFinderInterface
    from models import ProcessingConfig


class ClassNameFinder(ClassNameFinderInterface):
    """类名查找器 - 调用FindNameFromJar2.py脚本"""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.script_path = config.script_path
        self.project_path = config.project_path
        
        # 验证脚本文件是否存在
        if not os.path.exists(self.script_path):
            self.logger.warning(f"FindNameFromJar2.py脚本不存在: {self.script_path}")
    
    def find_exact_match(self, class_name: str) -> List[str]:
        """精确查找类名"""
        self.logger.info(f"开始精确查找类名: {class_name}")
        
        try:
            # 调用Python脚本
            result = self._execute_script(class_name)
            
            if result:
                # 解析脚本输出，提取完整类名
                matches = self._parse_script_output(result, class_name)
                self.logger.info(f"精确查找到 {len(matches)} 个匹配: {matches}")
                return matches
            else:
                self.logger.info(f"未找到精确匹配: {class_name}")
                return []
                
        except Exception as e:
            self.logger.error(f"精确查找类名时发生错误: {e}")
            return []
    
    def find_fuzzy_match(self, class_name: str) -> List[str]:
        """模糊查找类名"""
        self.logger.info(f"开始模糊查找类名: {class_name}")
        
        try:
            # 首先尝试精确匹配
            exact_matches = self.find_exact_match(class_name)
            if exact_matches:
                return exact_matches
            
            # 如果精确匹配失败，尝试模糊匹配
            fuzzy_matches = self._find_similar_class_names(class_name)
            self.logger.info(f"模糊查找到 {len(fuzzy_matches)} 个候选: {fuzzy_matches}")
            return fuzzy_matches
            
        except Exception as e:
            self.logger.error(f"模糊查找类名时发生错误: {e}")
            return []
    
    def generate_class_variants(self, class_name: str) -> List[str]:
        """生成类名变体"""
        return self._generate_class_name_variations(class_name)
    
    def _execute_script(self, class_name: str) -> Optional[str]:
        """执行FindNameFromJar2.py脚本"""
        if not os.path.exists(self.script_path):
            self.logger.warning("FindNameFromJar2.py脚本不存在，跳过脚本调用")
            return None
        
        try:
            # 构建命令
            cmd = [
                'python', 
                self.script_path,
                class_name,
                self.project_path
            ]
            
            self.logger.debug(f"执行命令: {' '.join(cmd)}")
            
            # 执行脚本
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=self.config.timeout_seconds,
                cwd=self.project_path
            )
            
            if process.returncode == 0:
                output = process.stdout.strip()
                self.logger.debug(f"脚本执行成功，输出: {output}")
                return output
            else:
                self.logger.warning(f"脚本执行失败，错误: {process.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            self.logger.error(f"脚本执行超时 ({self.config.timeout_seconds}秒)")
            return None
        except Exception as e:
            self.logger.error(f"执行脚本时发生错误: {e}")
            return None
    
    def _parse_script_output(self, output: str, target_class: str) -> List[str]:
        """解析脚本输出，提取完整类名"""
        matches = []
        
        # 按行分割输出
        lines = output.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 查找包含"全限定名"的行
            if "全限定名:" in line:
                # 提取全限定名
                parts = line.split("全限定名:")
                if len(parts) > 1:
                    full_class_name = parts[1].strip()
                    if full_class_name and full_class_name not in matches:
                        matches.append(full_class_name)
            
            # 查找包含"找到类"的行
            elif "找到类" in line and "于 JAR:" in line:
                # 从输出中提取类名信息
                if target_class in line:
                    # 这里可以进一步解析JAR路径等信息
                    continue
        
        return matches
    
    def _find_similar_class_names(self, class_name: str) -> List[str]:
        """查找相似的类名（模糊匹配）"""
        similar_names = []
        
        # 常见的类名变换模式
        variations = self._generate_class_name_variations(class_name)
        
        for variation in variations:
            matches = self.find_exact_match(variation)
            similar_names.extend(matches)
        
        # 去重并返回
        return list(set(similar_names))
    
    def _generate_class_name_variations(self, class_name: str) -> List[str]:
        """生成类名的可能变体"""
        variations = []
        
        # 原始类名
        variations.append(class_name)
        
        # 常见的后缀变换
        suffixes_to_try = ['Service', 'Impl', 'DTO', 'VO', 'Entity', 'Po', 'Do']
        suffixes_to_remove = ['Service', 'Impl', 'DTO', 'VO', 'Entity', 'Po', 'Do']
        
        # 添加后缀
        for suffix in suffixes_to_try:
            if not class_name.endswith(suffix):
                variations.append(class_name + suffix)
        
        # 移除后缀
        for suffix in suffixes_to_remove:
            if class_name.endswith(suffix):
                base_name = class_name[:-len(suffix)]
                if base_name:
                    variations.append(base_name)
        
        # 常见的前缀变换
        prefixes_to_try = ['Base', 'Abstract', 'Default']
        
        for prefix in prefixes_to_try:
            if not class_name.startswith(prefix):
                variations.append(prefix + class_name)
        
        # 驼峰命名变换
        if '_' in class_name:
            # 下划线转驼峰
            camel_case = self._snake_to_camel(class_name)
            variations.append(camel_case)
        
        # 移除重复
        return list(set(variations))
    
    def _snake_to_camel(self, snake_str: str) -> str:
        """下划线命名转驼峰命名"""
        components = snake_str.split('_')
        return ''.join(word.capitalize() for word in components)
    
    def get_class_suggestions(self, class_name: str, max_suggestions: int = 5) -> List[dict]:
        """获取类名建议，包含详细信息"""
        suggestions = []
        
        # 精确匹配
        exact_matches = self.find_exact_match(class_name)
        for match in exact_matches[:max_suggestions]:
            suggestions.append({
                'class_name': match,
                'match_type': 'exact',
                'confidence': 1.0,
                'reason': '精确匹配'
            })
        
        # 如果精确匹配不足，添加模糊匹配
        if len(suggestions) < max_suggestions:
            fuzzy_matches = self.find_fuzzy_match(class_name)
            remaining_slots = max_suggestions - len(suggestions)
            
            for match in fuzzy_matches[:remaining_slots]:
                if match not in [s['class_name'] for s in suggestions]:
                    confidence = self._calculate_similarity(class_name, match)
                    suggestions.append({
                        'class_name': match,
                        'match_type': 'fuzzy',
                        'confidence': confidence,
                        'reason': f'相似度: {confidence:.2f}'
                    })
        
        return suggestions
    
    def _calculate_similarity(self, str1: str, str2: str) -> float:
        """计算两个字符串的相似度"""
        # 简单的编辑距离相似度计算
        if not str1 or not str2:
            return 0.0
        
        # 提取类名（去掉包名）
        class1 = str1.split('.')[-1]
        class2 = str2.split('.')[-1]
        
        # 计算编辑距离
        distance = self._edit_distance(class1.lower(), class2.lower())
        max_len = max(len(class1), len(class2))
        
        if max_len == 0:
            return 1.0
        
        similarity = 1.0 - (distance / max_len)
        return max(0.0, similarity)
    
    def _edit_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self._edit_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]