# 06 能耗数据查询

## EnergyConsumptionDao（能耗数据查询）

1.  提供能耗数据查询，包括redis中的数据以及数据库中的数据；
    
2.  方法如下：
    

1.  查询非分时能耗：queryEnergyConsumption(BaseVo node, long st, long et, int cycle, @Null List<Integer> energyTypes)
    
2.  查询非分时能耗：queryEnergyConsumption(BaseVo node, LocalDateTime st, LocalDateTime et, Integer cycle, @NotEmpty List<Integer> energyTypes)
    
3.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, long st, long et, int cycle, @NotEmpty Collection<Integer> energyTypes)
    
4.  查询非分时能耗数据：queryNoTsEnergyConsumption(EnergyConsumptionPeriodQueryDTO param)
    
5.  查询简化版本的能耗数据：querySimpleEnergyConsumption(EnergyConsumptionPeriodQueryDTO param)
    
6.  查询总能耗：queryEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, LocalDateTime st, LocalDateTime et, Integer cycle, @Null Collection<Integer> energyTypes)
    
7.  查询非分时能耗数据：queryNoTsEnergyConsumption(EnergySingleLabelQueryDTO queryDTO)
    
8.  查询非分时能耗(已废弃)：queryEnergyConsumption(@NotEmpty List<Long> ids, String modelLabel, Long startTime, Long endTime, Integer cycle, @Null List<Integer> energyTypes)
    
9.  查询分时能耗数据：queryTsEnergyConsumption(EnergyTimeShareQueryDTO queryDTO)
    
10.  查询分时能耗：queryTsEnergyConsumption(@NotEmpty List<? extends BaseEntity> nodes, long st, long et, @NotNull Integer cycle, @NotNull Integer energyType, List<String> tsIds)
    
11.  查询能耗信息：queryEnergyConsumption(List<QueryEnergyConsumptionCriterial> QueryEnergyConsumptionCriterialList)
    
12.  查询非分时能耗(已废弃)：queryNonTsEnergyConsumption(Long\[\] times, int cycle, List<BaseVo> nodes)
    
13.  查询非分时能耗：queryNonTsEnergyConsumption(List<Long\[\]> times, int cycle, BaseVo node, int energyType)
    
14.  查询非分时能耗：queryNonTsEnergyConsumption(Collection<DimConsumptionNodeAndTimeModel> dimConsumptionNodeAndTimeModels, int cycle, int energyType)
    
15.  查询所有的能耗：queryAllEnergyConsumption(Collection<DimConsumptionNodeAndTimeModel> dimConsumptionNodeAndTimeModels, int cycle, int energyType)
    
16.  查询所有能耗数据：queryAllEnergyConsumption(@NotEmpty List<BaseVo> nodes, LocalDateTime startTime, LocalDateTime endTime, Integer cycle, Collection<Integer> energyTypes)
    
17.  查询能耗，包括分时和非分时能耗：queryAllEnergyConsumption(List<Long\[\]> times, int cycle, BaseVo node, int energyType)
    
18.  批量插入能耗数据：insertEnergyConsumption(Collection<EnergyConsumption> energyConsumptionList)
    
19.  查询能耗：queryEnergyConsumption(EnergyConsumptionSearchDto dto)
    
20.  查询总能耗(已废弃)：queryTotalEnergyConsumption(@NotEmpty List<BaseVo> nodes, LocalDateTime startTime, LocalDateTime endTime, Integer cycle, Collection<Integer> energyTypes)
    
21.  查询非分时能耗数据：queryNoTsEnergyConsumption(EnergyConsumptionQueryDTO obj)
    
22.  查询非分时能耗数据：queryNoTsEnergyConsumption(EnergyMultiEnergyTypeDTO obj)
    
23.  查询总能耗数据，注意该方法是模糊查询，查询出来的数据可能比需要的会多一些：queryTotalEnergyConsumption(List<EnergyConsumptionQueryDto> energyConsumptionQueryDtoList)
    
24.  查询能耗：queryEnergyConsumption(List<Long> logTimes, List<Long> objectIds, List<Integer> energyTypes, String modelLabel, Integer aggregationCycle)
    
25.  查询多节点,多时间点能耗：queryEnergyConsumptionBatch(List<Long> logTimes, List<? extends EntityWithName> nodes, List<Integer> energyTypes, Integer aggregationCycle)
    
26.  查询能耗数据：queryEnergyConsumption(Long st, Long et, Long objectId, String objectLabel, Integer aggregationCycle)
    
27.  查询能耗：queryEnergyConsumption(MutableTriple<GroupNode, String, List<Long>> nodeTriple, Collection<Long> logTimes, Collection<Integer> energyTypes, Integer cycle)
    
28.  查询能耗：queryEnergyConsumption(@NotEmpty Collection<Long> logTimes, Integer energyType, Integer cycle, boolean timePeriod, List<MutableTriple<GroupNode, String, List<Long>>> nodeTriples)
    
29.  查询离散时间点能耗信息：queryEnergyConsumption(List<Long> ids, String modelLabel, List<LocalDateTime> logtimeList, Integer cycle, List<Integer> energyTypes)
    
30.  查询能耗：queryEnergyConsumption(List<MutablePair<List<Long>, Integer>> timeCyclePairs, String modelLabel, List<Long> objectIds, List<Integer> energyTypes)
    
31.  按时间分组查询能耗：queryEnergyConsumption(List<MutablePair<Long, Long>> timePairs, String objectLabel, Collection<Long> objectIds, Integer energyType, Integer cycle)
    
32.  查询云平台能耗：queryCloudPlatformEnergyConsumption(QueryEnergyConsumptionCriterial queryEnergyConsumptionCriterialDTO)
    
33.  查询户用能耗：queryHouseholdEnergyConsumption(QueryEnergyConsumptionCriterial queryEnergyConsumptionCriterial)
    
34.  查询能耗浮动前数据：queryEnergyFloatBefore(QueryEnergyConsumptionCriterial queryEnergyConsumptionCriterial)
    
35.  模糊查询所有的能耗数据，包括总能耗和分时能耗数据(已废弃)：queryAllEnergyConsumptionFuzzy(@NotEmpty Collection<BaseVo> nodes, @NotEmpty Collection<Long> logTimes, @NotEmpty Collection<Integer> energyTypes, @NotEmpty Collection<Integer> cycles)
    
36.  查询总能耗：queryTotalEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, @NotNull Long time, @NotNull Integer energyType, @NotNull Integer cycle)
    
37.  查询单个节点当天、当月、当年的所有能源类型的能耗值：queryAllCycleEnergyConsumption(BaseVo node, Long currentDay, List<Integer> energyTypes)
    
38.  根据条件查询分时能耗数据：queryTsEnergyConsumption(EnergyWithEnergyTypeQueryDTO query)
    
39.  根据条件查询非分时能耗数据：queryNoTsEnergyConsumption(EnergyWithEnergyTypeQueryDTO query)
    
40.  写入能耗数据：upsertEnergyConsumption(List<EnergyConsumption> energyList)
    
41.  根据条件查询非分时能耗数据：queryNoTsEnergyConsumption(EnergyBatchQueryDTO query)
    
42.  根据条件查询分时能耗数据：queryTsEnergyConsumption(EnergyBatchTsQueryDTO query)
    
43.  查询总能耗：queryNotTsEnergyConsumption(@NotEmpty Collection<BaseVo> nodes, LocalDateTime st, LocalDateTime et, Integer cycle, @Null Collection<Integer> energyTypes)；
    

## MultiDimEnergyConsumptionDao（分项能耗数据查询）

1.  该类提供了分项多维度能耗的查询，注意与固定多维度能耗数据进行区分；
    
2.  方法如下：
    

1.  查询多维度能耗，注意该方法是模糊查询，查询出来的数据可能比需要的会多一些：queryTotalEnergyConsumption(List<DimEnergyConsumptionQueryDto> energyConsumptionQueryDtoList)
    
2.  查询总多维度能耗数据，不包含分时数据：queryTotalMultiDimEnergyConsumption(MultiDimEnergyConsumptionQueryDTO queryDTO)
    
3.  查询所有多维度能耗数据，包含分时数据：queryAllMultiDimEnergyConsumption(MultiDimEnergyConsumptionQueryDTO queryDTO)
    
4.  查询多维度能耗(已废弃)：queryMultiConsumptions(@NotEmpty Collection<Long> logTimes, Integer energyType, Integer cycle, boolean timePeriod, Collection<String> dimensionTypes, List<MutableTriple<GroupNode, String, List<Long>>> nodeTriples)
    
5.  查询分时或非分时多维度能耗：queryTsOrNonTsMultiEnergyConsumption(Collection<BaseVo> nodes, long st, long et, Integer cycle, Integer energyType, Collection<String> dimensionTypes, boolean timePeriod)
    
6.  查询多维度能耗：queryMultiEnergyConsumption(Long st, Long et, Integer aggregationCycle, Integer energyType, String modelLabel, Collection<String> dimTagIds, Collection<Long> objectIds)
    
7.  查询多维度能耗：queryMultiEnergyConsumption(Collection<Long> logTimes, Integer cycle, Collection<Integer> energyTypes, String nodeLabel, Collection<Long> objectIds, Collection<String> dimTagIds)
    
8.  查询多维度能耗：queryMultiEnergyConsumption(Collection<String> labels, Collection<Long> logTimes, Collection<MutablePair<String, List<Long>>> labelIds, Collection<String> tagIds, Integer energyType, Integer cycle)
    
9.  查询多维度能耗：queryMultiEnergyConsumption(@NotNull Long startTime, @NotNull Long endTime, @NotNull Integer cycle, @Null Integer energyType, @NotEmpty Collection<String> tagIds)
    
10.  查询多维度能耗：queryMultiEnergyConsumption(@NotNull LocalDateTime startTime, @NotNull LocalDateTime endTime, @NotNull Integer cycle, @Null Integer energyType, @NotEmpty Collection<String> tagIds)
    
11.  查询多维能源消耗总量：queryTotalMultiDimEnergyConsumption(ItemizedEnergyQueryDTO queryDTO)
    
12.  查询多维能源消耗总量：queryTotalMultiDimEnergyConsumption(MultiNodeDimEnergyQueryDTO queryDTO)
    
13.  批量插入多维能耗数据：upsertEnergyConsumption(List<MultiDimEnergyConsumption> energyList)
    

## CombineEnergyService

1.  该类提供了固定维度节点和管理层级节点能耗的查询；
    
2.  方法如下：
    
    1.  查询子层级非分时能耗数据：queryChildNoTsEnergyConsumption(EnergyConsumptionQueryDTO newQuery)
        
    2.  查询能耗数据：queryNoTsEnergyConsumption(EnergyConsumptionQueryDTO query)
        
    3.  查询批量节点非分时能耗数据：queryBatchNodeNoTsEnergyConsumption(EnergyConsumptionPeriodQueryDTO query)
        
    4.  查询非分时能耗数据：queryNoTsEnergyConsumption(EnergyConsumptionPeriodQueryDTO query)
        
    5.  查询批量节点非分时能耗数据：queryNoTsEnergyConsumption(EnergyBatchQueryDTO query, Map<String, List<StartAndEndTime>> nodeAndEffTimeMap, Long dimConfigId)
        
    6.  查询非分时能耗数据：queryNoTsEnergyConsumption(EnergySingleLabelQueryDTO query)
        
    7.  查询分时能耗数据：queryTsEnergyConsumption(EnergyTimeShareQueryDTO query)
        
    8.  查询非分时能耗数据：queryNoTsEnergyConsumption(EnergyMultiEnergyTypeDTO query)
        
    9.  查询非分时能耗数据：queryNoTsEnergyConsumption(EnergyWithEnergyTypeQueryDTO energyBatchQueryDTO, Map<String, List<StartAndEndTime>> nodeAndEffTimeMap, Long dimTreeConfigId)